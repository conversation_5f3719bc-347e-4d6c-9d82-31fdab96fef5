'use client'

import { BlocksRenderer, type BlocksContent } from '@strapi/blocks-react-renderer'
import { Link } from '@/navigation'
import { isInternalUrl } from '@/lib/utils'
import { isAbsoluteUrl } from 'next/dist/shared/lib/utils'

export function ArticleBlocksRenderer({ content }: { content: BlocksContent }) {
  return (
    <BlocksRenderer
      content={content}
      blocks={{
        link: ({ children, url }) => {
          if (isAbsoluteUrl(url)) {
            if (isInternalUrl(url)) {
              return <a href={url}>{children}</a>
            } else {
              return (
                <a href={url} target="_blank" rel="nofollow noreferrer noopener">
                  {children}
                </a>
              )
            }
          } else {
            return (
              <Link prefetch={false} href={url}>
                {children}
              </Link>
            )
          }
        },
      }}
    />
  )
}
