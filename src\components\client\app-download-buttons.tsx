'use client'
import { useEffect, useState } from 'react'
import { FaWindows, FaAppStore } from 'react-icons/fa'
import { <PERSON>dal, Button } from 'flowbite-react'
import { FaApple } from 'react-icons/fa'
import { useTranslations } from 'next-intl'
import { DesktopInfo } from '@/types/desktop'
import { API_BASE_URL } from '@/config/global'


export function AppDownloadButtons() {
  const [osType, setOsType] = useState<'windows' | 'mac'>('windows')
  const [openChipModal, setOpenChipModal] = useState(false)
  const [desktopInfo, setDesktopInfo] = useState<DesktopInfo | null>(null)
  const t = useTranslations('appDownloadButtons')

  // 获取桌面应用信息
  async function fetchDesktopInfo() {
      const res = await fetch(`${API_BASE_URL}/desktop/info`)
      const data = await res.json()
      setDesktopInfo(data)
  }

  useEffect(() => {
     // 获取桌面应用信息
    fetchDesktopInfo()
    // Detect OS
    const userAgent = window.navigator.userAgent.toLowerCase()
    if (userAgent.includes('mac')) {
      // 是否可以使用更复杂的逻辑来判断是intel还是silicon芯片？
      setOsType('mac')
    } else {
      setOsType('windows')
    }
  }, [])

  return (
    <>
      <div className="flex flex-col items-center gap-4 sm:flex-row md:gap-6">
        <a
          href={desktopInfo?.downloadUrls.windows}
          target="_blank"
          rel="nofollow"
          className={`px-6 py-2 rounded-lg flex items-center gap-4 transition-colors ${
            osType === 'mac'
              ? 'border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white bg-white'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          <FaWindows className="w-8 h-8" />
          <div>
            <div className="text-base md:text-lg font-semibold">{t('downloadForWindows')}</div>
            <div className="text-sm opacity-90">{t('windowsDesc')}</div>
          </div>
        </a>
        <button
          onClick={() => setOpenChipModal(true)}
          className={`px-6 py-2 rounded-lg flex items-center gap-4 transition-colors ${
            osType === 'mac'
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white bg-white'
          }`}
        >
          <FaAppStore className="w-8 h-8" />
          <div>
            <div className="text-base md:text-lg font-semibold">{t('downloadForMac')}</div>
            <div className="text-sm opacity-90">{t('macDesc')}</div>
          </div>
        </button>
      </div>
      <Modal show={openChipModal} onClose={() => setOpenChipModal(false)} popup dismissible>
        <Modal.Header />
        <Modal.Body>
          <div className="text-center py-8">
            <h3 className="text-3xl font-bold mb-4">{t('macChipModalTitle')}</h3>
            <p className="text-base md:text-xl text-gray-600 mb-12 flex items-center justify-center gap-2 flex-wrap">
              {t.rich('macChipModalDescription', { apple: (chunks) => <FaApple className="w-5 h-5" /> })}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button size="xl" as="a" href={desktopInfo?.downloadUrls.macAppleSilicon} target="_blank" rel="nofollow">
                {t('downloadForAppleChip')}
              </Button>
              <Button size="xl" as="a" href={desktopInfo?.downloadUrls.macIntel} target="_blank" rel="nofollow">
                {t('downloadForIntel')}
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </>
  )
}
