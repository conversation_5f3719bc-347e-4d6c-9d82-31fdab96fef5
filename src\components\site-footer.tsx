import { Locale, siteConfig } from '@/config/global'
import { Link } from '@/navigation'
import { siteMetadatas } from '@/config/downloader'
import { MILLISECONDS_IN_SECOND } from '@gongyinshi/common/datetime'
import { getTranslations, getLocale } from 'next-intl/server'
import { ArticleMeta } from '@/types/global'
import { logger } from '@gongyinshi/kit/logger'
import { ChevronsRight } from 'lucide-react'
import { FaDiscord, FaTelegram, FaWindows, FaAppStore } from 'react-icons/fa'
import { IoMailSharp } from 'react-icons/io5'
import { Tooltip } from 'flowbite-react'

async function getLatestTutorial(locale: Locale): Promise<ArticleMeta[]> {
  try {
    const res = await fetch(
      `${process.env.CMS_API_BASE_URL}/api/articles?sort[0]=publishedAt:desc&filters[category][$eq]=tutorial&fields[0]=title&fields[1]=slug&fields[2]=description&pagination[pageSize]=4&pagination[page]=1&locale=${locale}`,
      {
        method: 'GET',
        headers: {
          Authorization: `bearer ${process.env.CMS_API_TOKEN}`,
        },
        signal: AbortSignal.timeout(30 * MILLISECONDS_IN_SECOND),
        next: { tags: ['articles'] },
      }
    )
    const j = await res.json()
    return j.data
  } catch (e) {
    logger.error(e, `getLatestTutorial failed, locale: ${locale}`)
    return []
  }
}

export async function SiteFooter() {
  const t = await getTranslations('footer')
  const locale = (await getLocale()) as Locale
  const articles = await getLatestTutorial(locale)
  const tools = siteMetadatas[locale]

  return (
    <footer className="bg-gray-50 dark:bg-gray-800">
      <div className="container p-4 py-6 md:p-8 lg:p-10">
        <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
          <div>
            <h3 className="mb-6 text-sm font-semibold uppercase text-gray-400 dark:text-white">{t('helpCenter')}</h3>
            <ul className="space-y-4">
              <li className="text-gray-800">
                <a
                  href={siteConfig.socials.telegram}
                  target="_blank"
                  rel="nofollow"
                  className="hover:underline flex items-center gap-1"
                >
                  <FaTelegram className="w-5 h-5" /> {t('telegramGroup')}
                </a>
              </li>
              <li className="text-gray-800">
                <a
                  href={siteConfig.socials.discord}
                  target="_blank"
                  rel="nofollow"
                  className="hover:underline flex items-center gap-1"
                >
                  <FaDiscord className="w-5 h-5" /> {t('discordServer')}
                </a>
              </li>
              <li className="text-gray-800">
                <Tooltip content={siteConfig.email}>
                  <a href={`mailto:${siteConfig.email}`} className="hover:underline flex items-center gap-1">
                    <IoMailSharp className="w-5 h-5" /> {t('contact')}
                  </a>
                </Tooltip>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="mb-6 text-sm font-semibold uppercase text-gray-400 dark:text-white">Download</h3>
            <ul className="space-y-4">
              <li className="text-gray-800">
                <Link prefetch={false} href="/app" className="hover:underline flex items-center gap-1">
                  <FaWindows className="w-5 h-5" /> Windows
                </Link>
              </li>
              <li className="text-gray-800">
                <Link prefetch={false} href="/app" className="hover:underline flex items-center gap-1">
                  <FaAppStore className="w-5 h-5" /> macOS
                </Link>
              </li>
            </ul>
          </div>
          {/* <div>
            <h3 className="mb-6 text-sm font-semibold uppercase text-gray-400 dark:text-white">{t('languages')}</h3>
            <ul className="space-y-4">
              <LanguagesFooter />
            </ul>
          </div> */}
          <div>
            <h3 className="mb-6 text-sm font-semibold uppercase text-gray-400 dark:text-white">{t('tools')}</h3>
            <ul className="space-y-2 text-gray-500 dark:text-gray-400">
              {Object.entries(tools).map(([siteID, site]) => {
                return (
                  <li className="text-sm text-gray-800" key={siteID}>
                    <Link prefetch={false} href={`/${siteID}`} className="hover:underline">
                      {site.heading}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </div>
          <div>
            <h3 className="mb-6 text-sm font-semibold uppercase text-gray-400 dark:text-white">
              <Link prefetch={false} href="/articles/tutorial" className="flex items-center hover:underline">
                {t('articles')}
                <ChevronsRight className="h-5 w-5" />
              </Link>
            </h3>
            <ul className="space-y-2">
              {articles.map((item, index) => (
                <li className="text-sm text-gray-800" key={index}>
                  <Link prefetch={false} href={`/article/${item.slug}`} className="hover:underline">
                    {item.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <hr className="my-6 border-gray-200 dark:border-gray-700 sm:mx-auto lg:my-8" />
        <div className="block text-center text-sm text-gray-500 dark:text-gray-400">
          <p className="pb-1 flex gap-x-5 justify-center">
            <Link prefetch={false} href="/article/privacy-policy" className="hover:underline">
              {t('privacyPolicy')}
            </Link>
            <Link prefetch={false} href="/article/terms-of-service" className="hover:underline">
              {t('termsOfService')}
            </Link>
          </p>
          <p>
            Copyright &copy; {new Date().getFullYear()} {siteConfig.name}. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}
