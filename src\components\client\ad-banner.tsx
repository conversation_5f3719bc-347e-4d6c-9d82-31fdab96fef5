import Script from 'next/script'
import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { useLocale } from 'next-intl';

export function AdBanner() {
  const pathname = usePathname()
  const locale = useLocale();

  useEffect(() => {
    try {
      if (process.env.NODE_ENV !== 'production') return
      ;(window.adsbygoogle = window.adsbygoogle || []).push({})
    } catch (e) {
      console.error(e)
    }
  }, [pathname])

  return (
    <div className="w-full mt-6">
      {locale === 'zh' && new Date() < new Date('2025-07-01T20:00:00') && 
        <div className="flex justify-center gap-1">
          <a href="https://www.cac.mom?path=register&code=5kokDO5E" target="_blank" rel="nofollow noindex" className="font-semibold underline text-sm">海外加速器7元一月</a>
          <span className="text-[11px] self-end border rounded-[2px]">广告</span>
        </div>
      }
      {process.env.NODE_ENV === 'production' && (
        <Script
          id="_next-google-adsense"
          async
          src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2341115656300335"
          crossOrigin="anonymous"
          strategy="lazyOnload"
        />
      )}
      <ins
        className="adsbygoogle w-full mt-2"
        style={{ display: 'block' }}
        data-ad-client="ca-pub-2341115656300335"
        data-ad-slot="2061328536"
        data-ad-format="horizontal"
        data-full-width-responsive="true"
      ></ins>
    </div>
  )
}
