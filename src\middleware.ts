import createMiddleware from 'next-intl/middleware'
import { i18nConfig } from '@/config/global'

export default createMiddleware({
  // A list of all locales that are supported
  locales: i18nConfig.locales,
  // Used when no locale matches
  defaultLocale: i18nConfig.defaultLocale,
  // Don't use a locale prefix for the default locale
  localePrefix: i18nConfig.localePrefix,
  alternateLinks: false,
})

export const config = {
  // Matcher entries are linked with a logical "or", therefore
  // if one of them matches, the middleware will be invoked.
  matcher: [
    // Match all pathnames except for
    // - … if they start with `/api`, `/_next` or `/_vercel`
    // - … the ones containing a dot (e.g. `favicon.ico`)
    '/((?!api|monitoring|_next|_vercel|.*\\..*).*)',
    //   // However, match all pathnames within `/users`, optionally with a locale prefix
    //   '/([\\w-]+)?/users/(.+)'
  ],
}

// const intlMiddleware = createMiddleware({
//   // A list of all locales that are supported
//   locales: i18nConfig.locales,
//   // Used when no locale matches
//   defaultLocale: i18nConfig.defaultLocale,
//   // Don't use a locale prefix for the default locale
//   localePrefix: i18nConfig.localePrefix
// });

// export default async function middleware(request: NextRequest) {
//     const { pathname } = request.nextUrl;
//     const shouldHandle =
//         pathname === '/' ||
//         new RegExp(`^/(${i18nConfig.locales.join('|')})(/.*)?$`).test(request.nextUrl.pathname);

//     if (!shouldHandle) return;
//     return intlMiddleware(request);
// }
