# This workflow will build a docker container, publish and deploy it to Tencent Kubernetes Engine (TKE) when there is a push to the "main" branch.
#
# To configure this workflow:
#
# 1. Ensure that your repository contains the necessary configuration for your Tencent Kubernetes Engine cluster,
#    including deployment.yml, kustomization.yml, service.yml, etc.
#
# 2. Set up secrets in your workspace:
#    - TENCENT_CLOUD_SECRET_ID with Tencent Cloud secret id
#    - TENCENT_CLOUD_SECRET_KEY with Tencent Cloud secret key
#    - TENCENT_CLOUD_ACCOUNT_ID with Tencent Cloud account id
#    - TKE_REGISTRY_PASSWORD with TKE registry password
#
# 3. Change the values for the TKE_IMAGE_URL, TKE_REGION, TKE_CLUSTER_ID and DEPLOYMENT_NAME environment variables (below).

name: 发布到生产环境 🚀

on:
  workflow_dispatch:
  # push:
  #   branches: ['main']

# Environment variables available to all jobs and steps in this workflow
env:
  TKE_IMAGE_URL: hkccr.ccs.tencentyun.com/gystech/snapany-web
  DEPLOYMENT_NAME: snapany-web
  NAMESPACE: snapany

permissions:
  contents: read

jobs:
  setup-build-publish-deploy:
    name: Setup, Build, Publish, and Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Generate Image Tag
        id: tag
        run: |-
          TAG=$(TZ='Asia/Shanghai' date +%Y%m%d%H%M)-${GITHUB_SHA::6}
          echo "TAG=$TAG" >> $GITHUB_OUTPUT
          echo "Generated image tag: $TAG"

      - name: Build and Push Docker image
        run: |-
          docker login -u ${{ vars.TENCENT_CLOUD_ACCOUNT_ID }} -p '${{ secrets.TKE_REGISTRY_PASSWORD }}' ${TKE_IMAGE_URL}
          docker build -t ${TKE_IMAGE_URL}:${{ steps.tag.outputs.TAG }} .
          docker push ${TKE_IMAGE_URL}:${{ steps.tag.outputs.TAG }}

      - name: Set up SSH key
        run: |-
          mkdir -p ~/.ssh
          echo "${{ secrets.US_EAST_MAIN_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          echo "Host ${{ vars.HK_MAIN_IP }}
            IdentityFile ~/.ssh/id_rsa
            User ubuntu" > ~/.ssh/config
          ssh-keyscan -H ${{ vars.HK_MAIN_IP }} >> ~/.ssh/known_hosts

      - name: Deploy to TKE
        run: |-
          cd .k8s
          kustomize edit set image ${TKE_IMAGE_URL}:${{ steps.tag.outputs.TAG }}
          kustomize build . > manifests.yaml
          ssh ${{ vars.HK_MAIN_IP }} "kubectl apply -f -" < manifests.yaml && \
          ssh ${{ vars.HK_MAIN_IP }} "kubectl rollout status deployment/${DEPLOYMENT_NAME} -n ${NAMESPACE}" && \
          ssh ${{ vars.HK_MAIN_IP }} "kubectl get services -o wide -n ${NAMESPACE}"

      - name: Clean up SSH key
        if: always()
        run: |-
          rm -f ~/.ssh/id_rsa
          rm -f ~/.ssh/config
