'use client'
import { usePathname, <PERSON> } from '@/navigation'
import { LANGUAGES } from '@/config/global'
import { Locale } from '@/config/global'
import { useLocale } from 'next-intl'
import { getOtherLanguagePathname } from '@/lib/seo'

export function LanguagesFooter() {
  const locale = useLocale()
  const pathname = usePathname()
  const otherLanguagePathname = getOtherLanguagePathname(pathname) // other language pathname

  return (
    <>
      {Object.entries(LANGUAGES).map(([languageID, languageName]) => (
        <li className="text-gray-800" key={languageID}>
          <Link
            prefetch={false}
            href={languageID === locale ? pathname : otherLanguagePathname}
            locale={languageID as Locale}
            className="hover:underline"
          >
            {languageName}
          </Link>
        </li>
      ))}
    </>
  )
}
