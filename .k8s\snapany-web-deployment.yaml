apiVersion: apps/v1
kind: Deployment
metadata:
  name: snapany-web
  labels:
    app: snapany-web
spec:
  replicas: 2
  selector:
    matchLabels:
      app: snapany-web
  template:
    metadata:
      labels:
        app: snapany-web
    spec:
      containers:
        - name: snapany-web
          image: hkccr.ccs.tencentyun.com/gystech/snapany-web:latest
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: tke-docker-registry
---
apiVersion: v1
kind: Service
metadata:
  name: snapany-web
spec:
  selector:
    app: snapany-web
  ports:
    - port: 80
      targetPort: 80
