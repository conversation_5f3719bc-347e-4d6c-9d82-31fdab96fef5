// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Server Side",
      "type": "node-terminal",
      "request": "launch",
      "command": "npm run dev"
    },
    // {
    //   "name": "Debug Client Side",
    //   "type": "chrome",
    //   "request": "launch",
    //   "url": "http://localhost:3000"
    // },
    // {
    //   "name": "Debug Full Stack",
    //   "type": "node-terminal",
    //   "request": "launch",
    //   "command": "npm run dev",
    //   "serverReadyAction": {
    //     "pattern": "- Local:.+(https?://.+)",
    //     "uriFormat": "%s",
    //     "action": "debugWithChrome"
    //   }ay
    // },
    {
      "name": "Debug Current TS File",
      "type": "node",
      "request": "launch",
      "runtimeArgs": [
        "-r",
        "ts-node/register"
      ],
      "args": [
        "${file}"
      ]
    }
  ]
}