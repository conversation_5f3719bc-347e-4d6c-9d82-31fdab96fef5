import { Accordion, AccordionContent, AccordionPanel, AccordionTitle } from 'flowbite-react'
import { FAQ } from '@/config/downloader'

export function FAQSection({ faq }: { faq: FAQ[] }) {
  return (
    <div className="mx-auto max-w-screen-lg py-2">
      <Accordion flush collapseAll={false}>
        {faq.map((item, index) => (
          <AccordionPanel key={index}>
            <AccordionTitle className="bg-transparent dark:bg-transparent">{item.question}</AccordionTitle>
            <AccordionContent>{item.answer}</AccordionContent>
          </AccordionPanel>
        ))}
      </Accordion>
    </div>
  )
}
