import { logger } from '@gongyinshi/kit/logger'
import { MILLISECONDS_IN_SECOND } from '@gongyinshi/common/datetime'
import { API_BASE_URL } from '@/config/global'


/**
 * 获取桌面应用信息
 * 该接口已迁移到SnapAny后端项目中，这里是为了兼容旧版本的请求
 */
export async function GET() {
   try {
    const res = await fetch(`${API_BASE_URL}/desktop/info`,
      {
        method: 'GET',
        signal: AbortSignal.timeout(10 * MILLISECONDS_IN_SECOND),
        next: { revalidate: 60 * 10 }, // 10 minutes
      }
    )
    const data = await res.json()
    return Response.json(data)
  } catch (e) {
    logger.error(e, `fetch desktop info failed`)
    return Response.json({ message: 'fetch desktop info failed' }, { status: 500 })
  }
}
