import { pick } from 'lodash'
import { NextIntlClientProvider, useMessages, useTranslations } from 'next-intl'
import { notFound } from 'next/navigation'
import { Locale } from '@/config/global'
import type { Metadata } from 'next'
import { siteConfig } from '@/config/global'
import { genPageMetadata } from '@/lib/seo'
import { PartialRecord } from '@/types/global'
import { FAQ } from '@/config/downloader'
import { Download, Zap, PlaySquare, Captions, ListVideo, AudioLines } from 'lucide-react'
import { AppDownloadButtons } from '@/components/client/app-download-buttons'
import { Accordion, AccordionContent, AccordionPanel, AccordionTitle } from 'flowbite-react'

const FAQs: Record<Locale, FAQ[]> = {
  en: [
    {
      question: 'How do I use SnapAny?',
      answer: (
        <>
          <strong className="block text-lg mb-2">Using SnapAny is simple and quick:</strong>
          <ol className="list-decimal pl-5 space-y-2 text-gray-800">
            <li>Copy the share URL of the video or audio you wish to download.</li>
            <li>
              Click the <strong>'Paste Link'</strong> button to start downloading instantly!
            </li>
          </ol>
        </>
      ),
    },
    {
      question: 'Is SnapAny safe to use?',
      answer:
        'Yes! SnapAny is 100% safe. It contains no malware or trojans, and downloading it to your computer won’t compromise your security. Your personal information remains fully protected and confidential.',
    },
    {
      question: 'Can I transfer downloaded videos or audio files to my phone?',
      answer:
        'Absolutely! For easy access and file management, we recommend using cloud storage services like Dropbox or Google Drive. Simply set your download directory to a cloud folder, and your files will sync automatically to your phone for convenient access anytime, anywhere.',
    },
    {
      question: 'Can I download videos and audio in different formats using SnapAny?',
      answer:
        'Yes! SnapAny lets you download videos in multiple formats, including MP4 and MKV. You can also download audio in MP3, OGG, and M4A formats.',
    },
    {
      question: 'Is SnapAny completely free?',
      answer:
        'Yes! SnapAny is entirely free to use, with no hidden costs. Plus, it’s completely ad-free, so you can enjoy a seamless experience without interruptions.',
    },
  ],
  zh: [
    {
      question: '如何使用 SnapAny？',
      answer: (
        <>
          <strong className="block text-lg mb-2">使用 SnapAny 非常简单快捷：</strong>
          <ol className="list-decimal pl-5 space-y-2 text-gray-800">
            <li>复制您想下载的视频或音频的分享链接。</li>
            <li>
              点击 <strong>“粘贴链接”</strong> 按钮，即可立即开始下载！
            </li>
          </ol>
        </>
      ),
    },
    {
      question: 'SnapAny 安全吗？',
      answer:
        '是的！SnapAny 是 100% 安全的。它不含任何恶意软件或病毒，下载到您的电脑不会对您的安全造成威胁。您的个人信息将得到完全保护和保密。',
    },
    {
      question: '我可以将下载的视频或音频文件传输到手机吗？',
      answer:
        '当然可以！为了方便访问和文件管理，我们推荐使用云存储服务，例如 百度网盘 或 阿里云盘。只需将您的下载目录设置为云端文件夹，您的文件即可自动同步到手机，随时随地方便访问。',
    },
    {
      question: '我可以使用 SnapAny 下载不同格式的视频和音频吗？',
      answer: '是的！SnapAny 支持您以多种格式下载视频，例如 MP4 和 MKV。您还可以下载音频格式，例如 MP3、OGG 和 M4A。',
    },
    {
      question: 'SnapAny 是完全免费的吗？',
      answer: '是的！SnapAny 完全免费，没有任何隐性费用。而且它完全无广告，让您享受无干扰的流畅体验。',
    },
  ],
  ja: [
    {
      question: 'SnapAny の使い方は？',
      answer: (
        <>
          <strong className="block text-lg mb-2">SnapAny の使用はとても簡単です：</strong>
          <ol className="list-decimal pl-5 space-y-2 text-gray-800">
            <li>ダウンロードしたい動画や音声の共有URLをコピーします。</li>
            <li>
              <strong>「リンクを貼り付ける」</strong> ボタンをクリックすると、すぐにダウンロードが開始されます！
            </li>
          </ol>
        </>
      ),
    },
    {
      question: 'SnapAny は安全ですか？',
      answer:
        'はい！SnapAny は 100% 安全です。マルウェアやトロイの木馬は一切含まれておらず、パソコンにダウンロードしてもセキュリティに影響を与えることはありません。個人情報は完全に保護され、機密が保持されます。',
    },
    {
      question: 'ダウンロードした動画や音声をスマートフォンに転送できますか？',
      answer:
        'もちろんです！簡単にアクセスし、ファイルを管理するために、Dropbox や Google Drive のようなクラウドストレージサービスを使用することをおすすめします。ダウンロードディレクトリをクラウドフォルダに設定するだけで、ファイルが自動的にスマートフォンと同期され、いつでもどこでも便利にアクセスできます。',
    },
    {
      question: 'SnapAny を使ってさまざまな形式の動画や音声をダウンロードできますか？',
      answer:
        'はい！SnapAny では、MP4 や MKV など、複数の形式で動画をダウンロードできます。また、MP3、OGG、M4A 形式で音声をダウンロードすることも可能です。',
    },
    {
      question: 'SnapAny は完全に無料ですか？',
      answer:
        'はい！SnapAny は完全に無料で利用できます。隠れた費用は一切ありません。また、広告が一切ないため、邪魔されることなくスムーズな体験をお楽しみいただけます。',
    },
  ],
  es: [
    {
      question: '¿Cómo usar SnapAny?',
      answer: (
        <>
          <strong className="block text-lg mb-2">Usar SnapAny es fácil y rápido:</strong>
          <ol className="list-decimal pl-5 space-y-2 text-gray-800">
            <li>Copia el enlace compartido del video o audio que deseas descargar.</li>
            <li>
              Haz clic en el botón <strong>'Pegar Enlace'</strong> para comenzar a descargar al instante.
            </li>
          </ol>
        </>
      ),
    },
    {
      question: '¿Es seguro usar SnapAny?',
      answer:
        '¡Sí! SnapAny es 100% seguro. No contiene malware ni troyanos, y descargarlo en tu computadora no comprometerá tu seguridad. Tu información personal permanece completamente protegida y confidencial.',
    },
    {
      question: '¿Puedo transferir los videos o archivos de audio descargados a mi teléfono?',
      answer:
        '¡Por supuesto! Para un acceso fácil y gestión de archivos, recomendamos usar servicios de almacenamiento en la nube como Dropbox o Google Drive. Simplemente configura tu directorio de descargas en una carpeta en la nube, y tus archivos se sincronizarán automáticamente con tu teléfono para un acceso conveniente en cualquier momento y lugar.',
    },
    {
      question: '¿Puedo descargar videos y audios en diferentes formatos usando SnapAny?',
      answer:
        '¡Sí! SnapAny te permite descargar videos en múltiples formatos, incluyendo MP4 y MKV. También puedes descargar audios en formatos como MP3, OGG y M4A.',
    },
    {
      question: '¿SnapAny es completamente gratuito?',
      answer:
        '¡Sí! SnapAny es totalmente gratuito, sin costos ocultos. Además, está completamente libre de anuncios, para que disfrutes de una experiencia sin interrupciones.',
    },
  ],
}

const METADATA: Record<
  Locale,
  {
    title: string
    description: string
    applicationName: string
  }
> = {
  en: {
    title: 'Download SnapAny Desktop App for Windows and Mac',
    description:
      'SnapAny Desktop App is a free and powerful video downloader and converter for Windows and Mac. It allows you to download videos, music, and images from 10,000+ sites in one click. Fast, free, and powerful.',
    applicationName: 'SnapAny Desktop App',
  },
  zh: {
    title: '下载 SnapAny 电脑软件（适用于 Windows 和 Mac）',
    description:
      'SnapAny 电脑软件是一款免费且强大的视频下载和转换工具，适用于 Windows 和 Mac。它支持一键下载来自 10,000+ 网站的视频、音乐和图片。快速、免费且功能强大。',
    applicationName: 'SnapAny 电脑软件',
  },
  ja: {
    title: 'Windows および Mac 用 SnapAny デスクトップアプリをダウンロード',
    description:
      'SnapAny デスクトップアプリは、Windows および Mac に対応した無料で強力な動画ダウンロード＆変換ツールです。10,000 以上のサイトから動画、音楽、画像をワンクリックでダウンロードできます。高速、無料、そしてパワフルです。',
    applicationName: 'SnapAny デスクトップアプリ',
  },
  es: {
    title: 'Descargar la aplicación de escritorio SnapAny para Windows y Mac',
    description:
      'SnapAny Desktop App es una herramienta gratuita y poderosa para descargar y convertir videos, disponible para Windows y Mac. Permite descargar videos, música e imágenes de más de 10,000 sitios con un solo clic. Rápido, gratuito y poderoso.',
    applicationName: 'SnapAny Desktop App',
  },
}

export async function generateMetadata({ params }: { params: { locale: Locale } }): Promise<Metadata> {
  const metadata = METADATA[params.locale]
  if (!metadata) {
    notFound()
  }

  return genPageMetadata({
    title: metadata.title,
    description: metadata.description,
    pathname: '/app',
    locale: params.locale,
    applicationName: metadata.applicationName,
  })
}

// const testimonials = [
//   {
//     quote: "SnapAny is an incredible tool for saving videos and subtitles in top quality—even in 4K! The interface is clean, the downloads are fast, and the performance is rock solid. I love that I can download entire playlists or extract audio in MP3 format effortlessly. Highly recommended!",
//     name: "Evelyn Hutton",
//   },
//   {
//     quote: "I travel a lot and needed a simple way to save videos and music for offline use. SnapAny does exactly that! It's fast, easy to use, and gives me complete control over file formats and quality. Now, I can't imagine traveling without it!",
//     name: "Brian Smith",
//   },
//   {
//     quote: "As a content creator, I use SnapAny almost daily. It allows me to extract high-quality videos, audio, and subtitles from multiple platforms effortlessly. The reliability and efficiency have made my workflow so much smoother. Definitely a game-changer!",
//     name: "William Ahmad",
//   },
//   {
//     quote: "I was surprised to find such a powerful, feature-rich software that's completely free. SnapAny supports multi-language subtitles, different audio tracks, and even downloads video thumbnails. The batch download feature for playlists and channels is a massive time-saver!",
//     name: "Samantha J.",
//   },
//   {
//     quote: "I'm not very tech-savvy, but SnapAny makes downloading videos and music ridiculously simple. The interface is intuitive, and the process is straightforward. Whether I need MP3s or full HD videos, it delivers every time!",
//     name: "David R.",
//   },
//   {
//     quote: "I've tried many downloaders before, but SnapAny stands out because it's free, fast, and doesn't bombard you with ads or limitations. Whether it's a single video or an entire playlist, it works flawlessly. Highly recommended for anyone who needs a reliable downloader!",
//     name: "Emily W.",
//   }
// ];

export default function Page({ params }: { params: { locale: Locale } }) {
  const messages = useMessages()
  const t = useTranslations('app')
  const faqs = FAQs[params.locale] || []

  return (
    <main>
      {/* Hero Section */}
      <section className="bg-white dark:bg-gray-900 bg-[url('/images/hero-pattern.svg')] dark:bg-[url('/images/hero-pattern-dark.svg')]">
        <div className="bg-gradient-to-b from-blue-50 to-transparent dark:from-blue-900 w-full h-full">
          <div className="container flex flex-col gap-4 lg:gap-6 py-12 sm:py-16 items-center text-center">
            <h1 className="text-gray-900 tracking-tight text-3xl sm:text-4xl lg:text-5xl font-bold">{t('title')}</h1>
            <h2 className="text-gray-500 text-lg lg:text-xl">
              {t('subTitle')}
              <br /> {t('subTitle2')}
            </h2>
            <NextIntlClientProvider messages={pick(messages, 'appDownloadButtons')}>
              <AppDownloadButtons />
            </NextIntlClientProvider>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">{t('featureTitle')}</h2>
          <div className="grid md:grid-cols-2 gap-12  mx-auto">
            <div className="flex gap-4">
              <Download className="w-8 h-8 text-blue-600 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-xl mb-2">{t('feature1')}</h3>
                <p className="text-gray-600">{t('feature1Desc')}</p>
              </div>
            </div>
            <div className="flex gap-4">
              <Zap className="w-8 h-8 text-blue-600 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-xl mb-2">{t('feature2')}</h3>
                <p className="text-gray-600">{t('feature2Desc')}</p>
              </div>
            </div>
            <div className="flex gap-4">
              <PlaySquare className="w-8 h-8 text-blue-600 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-xl mb-2">{t('feature3')}</h3>
                <p className="text-gray-600">{t('feature3Desc')}</p>
              </div>
            </div>
            <div className="flex gap-4">
              <Captions className="w-8 h-8 text-blue-600 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-xl mb-2">{t('feature4')}</h3>
                <p className="text-gray-600">{t('feature4Desc')}</p>
              </div>
            </div>
            <div className="flex gap-4">
              <ListVideo className="w-8 h-8 text-blue-600 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-xl mb-2">{t('feature5')}</h3>
                <p className="text-gray-600">{t('feature5Desc')}</p>
              </div>
            </div>
            <div className="flex gap-4">
              <AudioLines className="w-8 h-8 text-blue-600 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-xl mb-2">{t('feature6')}</h3>
                <p className="text-gray-600">{t('feature6Desc')}</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Supported Sites */}
      <section className="py-20 bg-blue-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">{t('supportedSiteTitle')}</h2>
          <p className="text-xl text-gray-600 mb-12">
            {t('supportedSiteSubTitle')} <br />
            {t('supportedSiteSubTitle2')}
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8 max-w-5xl mx-auto">
            {[
              'YouTube',
              'Twitter',
              'Instagram',
              'TikTok',
              'VKontakte',
              'Facebook',
              'Reddit',
              'RedNote',
              'Vimeo',
              'BiliBili',
            ].map((site) => (
              <div key={site} className="flex items-center justify-center p-4 bg-gray-50 rounded-lg">
                <span className="font-medium">{site}</span>
              </div>
            ))}
          </div>
          {/* <button className="text-blue-600 hover:text-blue-700 font-medium mt-8">
            {t('fullListOfSupportedSites')}
          </button> */}
        </div>
      </section>

      {/* Testimonials */}
      {/* <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-8">Loved by Millions of Users Worldwide</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-md">
                <p className="text-gray-700 mb-4 text-left">"{testimonial.quote}"</p>
                <div className="flex items-center justify-between">
                  <div className="text-orange-500">
                    ★★★★★
                  </div>
                  <p className="text-gray-500">—{testimonial.name}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section> */}

      {/* FAQ */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4">{t('faq')}</h2>
          <p className="text-xl text-gray-600 text-center mb-12 max-w-3xl mx-auto">
            {t.rich('faqDesc', {
              discord: (chunks) => (
                <a
                  href={siteConfig.socials.discord}
                  target="_blank"
                  rel="nofollow"
                  className="text-blue-600 hover:text-blue-700"
                >
                  {chunks}
                </a>
              ),
              telegram: (chunks) => (
                <a
                  href={siteConfig.socials.telegram}
                  target="_blank"
                  rel="nofollow"
                  className="text-blue-600 hover:text-blue-700"
                >
                  {chunks}
                </a>
              ),
            })}
          </p>
          <div className="max-w-5xl mx-auto">
            <Accordion flush collapseAll={false}>
              {faqs.map((item, index) => (
                <AccordionPanel key={index}>
                  <AccordionTitle className="bg-transparent dark:bg-transparent">{item.question}</AccordionTitle>
                  <AccordionContent>{item.answer}</AccordionContent>
                </AccordionPanel>
              ))}
            </Accordion>
          </div>
        </div>
      </section>
    </main>
  )
}
