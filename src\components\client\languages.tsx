'use client'
import { usePathname, <PERSON> } from '@/navigation'
import { useLocale } from 'next-intl'
import { LANGUAGES } from '@/config/global'
import { Locale } from '@/config/global'
import { getOtherLanguagePathname } from '@/lib/seo'
import { DropdownItem } from 'flowbite-react'

export function Languages() {
  const locale = useLocale()
  const pathname = usePathname()
  const otherLanguagePathname = getOtherLanguagePathname(pathname) // other language pathname

  return (
    <>
      {Object.entries(LANGUAGES).map(([languageID, languageName]) => (
        <DropdownItem
          as={Link}
          key={languageID}
          prefetch={false}
          href={languageID === locale ? pathname : otherLanguagePathname}
          locale={languageID as Locale}
          className="block text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white"
        >
          {languageName}
        </DropdownItem>
      ))}
    </>
  )
}
