
export const LOCALES = ['en', 'zh', 'ja', 'es'] as const
// export const LOCALES = ['en', 'zh', 'ja', 'es', 'pt', 'fr'] as const;
export type Locale = (typeof LOCALES)[number]

export const LANGUAGES: Record<Locale, string> = {
  en: 'English',
  zh: '简体中文',
  ja: '日本語', // Japanese
  es: 'Español', // Spanish
  // pt: 'Português', // Portuguese
  // fr: 'Français', // French
} as const

export const siteConfig = {
  name: 'SnapAny',
  url: 'https://snapany.com',
  email: '<EMAIL>',
  ogImage: '/images/og.png',
  socials: {
    telegram: 'https://t.me/snapany_app',
    discord: 'https://discord.gg/dbKP6FKfvx',
  },
}

export const i18nConfig = {
  // A list of all locales that are supported
  // Spanish、Indonesian、Portuguese、Dutch、Thailand
  // locales: ['en', 'es', 'zh', 'tw', 'id', 'pt', 'nl', 'th', 'vn'],
  locales: LOCALES,
  // Used when no locale matches
  defaultLocale: 'en',
  // Don't use a locale prefix for the default locale
  localePrefix: 'as-needed',
} as const


export const SIGNATURE_KEY = '6HTugjCXxR'


export const API_BASE_URL = 'https://api.snapany.com'