'use client'

import { useEffect } from 'react'
import * as Sentry from '@sentry/nextjs'
import { Button } from 'flowbite-react'

export default function Error({ error }: { error: Error & { digest?: string } }) {
  useEffect(() => {
    Sentry.captureException(error)
  }, [error])

  return (
    <div className="flex flex-col items-center gap-4 py-8">
      <h2 className="font-semibold">Something went wrong!</h2>
      <Button onClick={() => location.reload()}>Try again</Button>
    </div>
  )
}
