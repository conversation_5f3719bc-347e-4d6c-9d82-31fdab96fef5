'use client'

import React, { useState } from 'react';
import { IconReload, IconDownload, IconCheck, IconPlayerStop } from '@tabler/icons-react';
import { DownloadStatus } from '@/types/download';
import { useTranslations } from 'next-intl';

interface DownloadButtonProps {
  status: DownloadStatus;
  onRetry?: () => void;
  onSave?: () => void;
  onStopRecording?: () => void; // 停止录制回调
  disabled?: boolean;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({
  status,
  onRetry,
  onSave,
  onStopRecording,
  disabled = false
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const t = useTranslations('downloadComponents');

  // 根据状态返回对应的按钮配置
  const getButtonConfig = () => {
    switch (status) {
      case DownloadStatus.DOWNLOADING:
        return null; // 下载中时不显示按钮

      case DownloadStatus.LIVE_RECORDING:
        return {
          icon: IconPlayerStop,
          text: t('stopRecording'),
          onClick: onStopRecording,
          className: 'flex flex-row justify-center items-center relative gap-2 px-5 py-[10px] rounded-lg hover:opacity-90 bg-red-600 min-w-fit h-[41px]',
          iconColor: '#FFFFFF',
          textColor: 'text-white'
        };

      case DownloadStatus.ERROR:
        return {
          icon: IconReload,
          text: t('retry'),
          onClick: onRetry,
          className: 'flex flex-row justify-center items-center relative gap-2 px-5 py-[10px] rounded-lg hover:opacity-90 bg-red-700 min-w-fit h-[41px]',
          iconColor: '#FFFFFF',
          textColor: 'text-white'
        };

      case DownloadStatus.COMPLETED:
        return {
          icon: IconDownload,
          text: t('save'),
          onClick: onSave,
          className: 'flex flex-row justify-center items-center relative gap-2 px-5 py-[10px] rounded-lg hover:opacity-90 bg-blue-700 min-w-fit h-[41px]',
          iconColor: '#FFFFFF',
          textColor: 'text-white'
        };

      case DownloadStatus.SAVED:
        // 悬浮时显示完成状态的样式
        if (isHovered) {
          return {
            icon: IconDownload,
            text: t('save'),
            onClick: onSave,
            className: 'flex flex-row justify-center items-center relative gap-2 px-5 py-[10px] rounded-lg hover:opacity-90 bg-blue-700 min-w-fit h-[41px]',
            iconColor: '#FFFFFF',
            textColor: 'text-white'
          };
        }
        // 默认显示已保存状态的样式
        return {
          icon: IconCheck,
          text: t('save'),
          onClick: onSave,
          className: 'flex flex-row justify-center items-center relative gap-2 hover:opacity-90 bg-white rounded-lg border border-blue-700 px-5 py-[10px] min-w-fit h-[41px]',
          iconColor: '#1A56DB',
          textColor: 'text-blue-700'
        };
    }
  };

  const config = getButtonConfig();

  // 如果配置为 null，则不渲染按钮
  if (!config) {
    return null;
  }

  const IconComponent = config.icon;

  return (
    <button
      onClick={config.onClick}
      disabled={disabled}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`${config.className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      <IconComponent
        size={16}
        stroke={1.33}
        color={config.iconColor}
        className="flex-none order-0 flex-grow-0"
      />
      <span className={`${config.textColor} font-medium text-sm leading-[150%] order-1 flex-grow-0`}>
        {config.text}
      </span>
    </button>
  );
};

export default DownloadButton;
