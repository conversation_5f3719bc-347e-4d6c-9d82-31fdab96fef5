import { But<PERSON> } from 'flowbite-react'
import { <PERSON> } from '@/navigation'

// Render the default Next.js 404 page when a route
// is requested that doesn't match the middleware and
// therefore doesn't have a locale associated with it.

export default function NotFound() {
  return (
    <html lang="en">
      <body>
        <section className="bg-white dark:bg-gray-900">
          <div className="mx-auto max-w-screen-xl px-4 py-8 lg:px-6 lg:py-16">
            <div className="mx-auto max-w-screen-sm text-center">
              <h1 className="mb-4 text-7xl font-extrabold tracking-tight text-primary-600 dark:text-primary-500 lg:text-9xl">
                404
              </h1>
              <p className="mb-4 text-3xl font-bold tracking-tight text-gray-900 dark:text-white md:text-4xl">
                Something's missing.
              </p>
              <p className="mb-4 text-lg text-gray-500 dark:text-gray-400">
                Sorry, we can't find that page. You'll find lots to explore on the home page.
              </p>
              <Button className="my-4 inline-flex" as={Link} href="/" prefetch={false}>
                Back to Homepage
              </Button>
            </div>
          </div>
        </section>
      </body>
    </html>
  )
}
