import { notFound } from 'next/navigation'
import { getRequestConfig } from 'next-intl/server'
import { i18nConfig } from '@/config/global'

export default getRequestConfig(async ({ requestLocale }) => {
  // Validate that the incoming `locale` parameter is valid
  let locale = await requestLocale
  if (!i18nConfig.locales.includes(locale as any)) notFound()
  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default,
  }
})
